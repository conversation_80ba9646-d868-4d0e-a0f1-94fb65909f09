"""
SAITS (Self-Attention Imputation Time Series) Model Implementation
Based on PyPOTS framework with custom adaptations for well log data

This module implements the SAITS model for well log imputation, providing
state-of-the-art self-attention mechanisms for time series imputation.
"""

import torch
import numpy as np
from typing import Dict, Any, Optional
import warnings

# Import PyPOTS components with error handling
try:
    from pypots.imputation import SAITS
    from pypots.optim import Adam
    PYPOTS_AVAILABLE = True
except ImportError as e:
    PYPOTS_AVAILABLE = False
    warnings.warn(f"PyPOTS not available: {e}")
    SAITS = None
    Adam = None

# Import GPU utilities
try:
    from utils.gpu_acceleration import GPUManager
    from utils.performance_monitor import get_performance_monitor
    from utils.gpu_fallback import get_fallback_manager, safe_cuda_empty_cache
    GPU_UTILS_AVAILABLE = True
except ImportError:
    print("⚠️ GPU utilities not available for SAITS model")
    GPU_UTILS_AVAILABLE = False
    GPUManager = None
    get_performance_monitor = None
    get_fallback_manager = None
    safe_cuda_empty_cache = lambda: None

from .base_model import BaseAdvancedModel

class SAITSModel(BaseAdvancedModel):
    """
    SAITS model wrapper for well log imputation.
    Provides compatibility with existing workflow while leveraging
    state-of-the-art self-attention mechanisms.
    
    SAITS (Self-Attention Imputation Time Series) is specifically designed
    for time series imputation with missing values, using transformer
    architecture with diagonal attention masks.
    """

    def __init__(self, n_features=4, sequence_len=64, n_layers=2,
                 d_model=256, n_heads=4, epochs=50, batch_size=32,
                 learning_rate=1e-3, dropout=0.1, device=None,
                 use_mixed_precision=True, validate_causality=True, **kwargs):
        """
        Initialize SAITS model with well log specific configurations and GPU support.

        Args:
            n_features: Number of log features (GR, NPHI, RHOB, target)
            sequence_len: Length of input sequences (depth windows)
            n_layers: Number of transformer layers
            d_model: Model dimension for attention mechanism
            n_heads: Number of attention heads
            epochs: Training epochs
            batch_size: Training batch size
            learning_rate: Optimizer learning rate
            dropout: Dropout rate for regularization
            device: Device to use ('cuda', 'cpu', or None for auto-detection)
            use_mixed_precision: Whether to use automatic mixed precision training
            validate_causality: Whether to validate temporal causality in attention patterns
            **kwargs: Additional model parameters
        """
        super().__init__(n_features, sequence_len, epochs, batch_size, learning_rate, **kwargs)
        
        if not PYPOTS_AVAILABLE:
            raise ImportError("PyPOTS is required for SAITS model. Please install with: pip install pypots")
        
        self.n_layers = n_layers
        self.d_model = d_model
        self.n_heads = n_heads
        self.dropout = dropout
        self.use_mixed_precision = use_mixed_precision
        self.validate_causality = validate_causality

        # Initialize GPU management with fallback
        self.gpu_manager = None
        self.fallback_manager = get_fallback_manager() if GPU_UTILS_AVAILABLE else None
        self.device = self._setup_device(device)
        self.performance_monitor = None

        # Validate parameters
        self._validate_parameters()

        print(f"🎯 SAITS Model Configuration:")
        print(f"   - Transformer layers: {n_layers}")
        print(f"   - Model dimension: {d_model}")
        print(f"   - Attention heads: {n_heads}")
        print(f"   - Dropout rate: {dropout}")
        print(f"   - Device: {self.device}")
        print(f"   - Mixed precision: {use_mixed_precision and self.device == 'cuda'}")
        print(f"   - Validate causality: {validate_causality}")

        # Initialize the model
        self._initialize_model()

    def _setup_device(self, device=None):
        """
        Setup the computing device (GPU/CPU) with fallback mechanisms.

        Args:
            device: Requested device ('cuda', 'cpu', or None for auto-detection)

        Returns:
            str: The selected device string for PyPOTS
        """
        # Use fallback manager if available
        if self.fallback_manager:
            if device is not None:
                safe_device = self.fallback_manager.get_safe_device(device)
            else:
                safe_device = self.fallback_manager.get_safe_device('cuda')

            if safe_device.type == 'cuda':
                # Initialize GPU manager if utilities are available
                if GPU_UTILS_AVAILABLE:
                    try:
                        self.gpu_manager = GPUManager()
                        print(f"🚀 SAITS using GPU with fallback protection: {safe_device}")
                        return 'cuda'  # PyPOTS expects string
                    except Exception as e:
                        print(f"⚠️ GPU manager initialization failed: {e}, using basic CUDA with fallback")

                # Basic CUDA setup with fallback protection
                try:
                    gpu_name = torch.cuda.get_device_name(0)
                    gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1e9
                    print(f"🚀 SAITS using GPU with fallback protection: {gpu_name} ({gpu_memory:.1f} GB)")
                except Exception as e:
                    print(f"⚠️ GPU info retrieval failed: {e}")
                return 'cuda'  # PyPOTS expects string
            else:
                print("💻 SAITS using CPU (GPU fallback activated)")
                return 'cpu'  # PyPOTS expects string

        # Fallback to original logic if fallback manager not available
        if device is not None:
            if device == 'cuda' and not torch.cuda.is_available():
                print("⚠️ CUDA requested but not available, falling back to CPU")
                return 'cpu'
            return device if isinstance(device, str) else str(device)

        # Auto-detect best device
        if torch.cuda.is_available():
            try:
                gpu_name = torch.cuda.get_device_name(0)
                gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1e9
                print(f"🚀 SAITS using GPU: {gpu_name} ({gpu_memory:.1f} GB)")
                return 'cuda'
            except Exception as e:
                print(f"⚠️ GPU setup failed: {e}, falling back to CPU")
                return 'cpu'
        else:
            print("💻 SAITS using CPU (CUDA not available)")
            return 'cpu'

    def _validate_parameters(self):
        """Validate model parameters for well log data."""
        if self.d_model % self.n_heads != 0:
            raise ValueError(f"d_model ({self.d_model}) must be divisible by n_heads ({self.n_heads})")
        
        if self.sequence_len < 16:
            print("⚠️ Warning: sequence_len < 16 may not be optimal for attention mechanisms")
        
        if self.n_features < 2:
            raise ValueError("SAITS requires at least 2 features for meaningful attention")
            
        if self.n_layers < 1 or self.n_layers > 8:
            print(f"⚠️ Warning: n_layers={self.n_layers} may not be optimal (recommended: 1-8)")
            
        if self.d_model < 64 or self.d_model > 1024:
            print(f"⚠️ Warning: d_model={self.d_model} may not be optimal (recommended: 64-1024)")

    def _initialize_model(self) -> None:
        """Initialize the PyPOTS SAITS model."""
        try:
            print(f"🔧 Initializing SAITS model...")
            
            # Adjust patience to be less than epochs
            patience = min(15, max(1, self.epochs // 3))

            self.model = SAITS(
                n_steps=self.sequence_len,
                n_features=self.n_features,
                n_layers=self.n_layers,
                d_model=self.d_model,
                d_ffn=self.d_model * 4,  # Feed-forward network dimension
                n_heads=self.n_heads,
                d_k=self.d_model // self.n_heads,  # Key dimension
                d_v=self.d_model // self.n_heads,  # Value dimension
                dropout=self.dropout,
                attn_dropout=self.dropout,
                diagonal_attention_mask=True,  # Causal attention for time series
                ORT_weight=1.0,  # Original Reconstruction Task weight
                MIT_weight=1.0,  # Masked Imputation Task weight
                batch_size=self.batch_size,
                epochs=self.epochs,
                patience=patience,  # Adjusted early stopping patience
                optimizer=Adam(lr=self.learning_rate),
                device=self.device,  # Use GPU-optimized device selection
                saving_path=None,  # No automatic saving
                model_saving_strategy=None
            )
            
            print(f"✅ SAITS model initialized successfully")
            print(f"   - Parameters: ~{self._estimate_parameters():,}")
            print(f"   - Memory usage: ~{self._estimate_memory_mb():.1f} MB")
            
        except Exception as e:
            print(f"❌ Failed to initialize SAITS model: {e}")
            raise RuntimeError(f"SAITS model initialization failed: {e}")

    def fit(self, train_data: torch.Tensor, truth_data: torch.Tensor,
            epochs: Optional[int] = None, batch_size: Optional[int] = None) -> None:
        """
        Train the SAITS model with GPU performance monitoring.

        Args:
            train_data: Training data with missing values
            truth_data: Complete ground truth data
            epochs: Number of epochs (optional override)
            batch_size: Batch size (optional override)
        """
        # Initialize performance monitoring if available
        if GPU_UTILS_AVAILABLE and get_performance_monitor:
            self.performance_monitor = get_performance_monitor()
            self.performance_monitor.start_monitoring()
            print("📊 Performance monitoring started for SAITS training")

        try:
            # Call parent fit method with performance monitoring context
            if self.performance_monitor:
                with self.performance_monitor.monitor_training_epoch():
                    super().fit(train_data, truth_data, epochs, batch_size)
            else:
                super().fit(train_data, truth_data, epochs, batch_size)

            # Clear GPU cache if available
            if self.device == 'cuda':
                safe_cuda_empty_cache()

        finally:
            # Stop monitoring and print report
            if self.performance_monitor:
                self.performance_monitor.stop_monitoring()
                if self.device == 'cuda':
                    print("\n📊 SAITS Training Performance Summary:")
                    summary = self.performance_monitor.get_performance_summary()
                    if summary['gpu_stats']:
                        gpu = summary['gpu_stats']
                        print(f"   • Average GPU Utilization: {gpu['avg_utilization']:.1f}%")
                        print(f"   • Peak GPU Memory: {gpu['max_memory_used']:.1f}GB")
                    if summary['training_stats']:
                        train = summary['training_stats']
                        print(f"   • Total Training Time: {train['total_training_time']:.1f}s")

    def _prepare_data(self, data: torch.Tensor, truth_data: Optional[torch.Tensor] = None) -> Dict[str, Any]:
        """Prepare data in PyPOTS format for SAITS."""
        return self._prepare_pypots_data(data, truth_data)
    
    def _estimate_parameters(self) -> int:
        """Estimate the number of model parameters."""
        # Rough estimation for SAITS model
        attention_params = self.n_layers * (
            # Self-attention parameters
            3 * self.d_model * self.d_model +  # Q, K, V projections
            self.d_model * self.d_model +      # Output projection
            # Feed-forward parameters
            self.d_model * (self.d_model // 2) + 
            (self.d_model // 2) * self.d_model
        )
        
        # Input/output embeddings and other components
        other_params = (
            self.n_features * self.d_model +  # Input embedding
            self.d_model * self.n_features +  # Output projection
            self.sequence_len * self.d_model  # Positional encoding
        )
        
        return attention_params + other_params
    
    def _estimate_memory_mb(self) -> float:
        """Estimate memory usage in MB."""
        # Rough estimation based on model size and batch size
        param_memory = self._estimate_parameters() * 4 / (1024 * 1024)  # 4 bytes per float32
        activation_memory = (
            self.batch_size * self.sequence_len * self.d_model * 
            self.n_layers * 4 / (1024 * 1024)
        )
        return param_memory + activation_memory

    def get_attention_weights(self, data: torch.Tensor) -> Optional[np.ndarray]:
        """
        Extract attention weights for visualization (if supported).

        Args:
            data: Input data tensor

        Returns:
            Attention weights array or None if not available
        """
        if not self.is_fitted:
            print("⚠️ Model must be fitted before extracting attention weights")
            return None

        try:
            # This would require modification of PyPOTS SAITS to expose attention weights
            # For now, return None and implement in future versions
            print("ℹ️ Attention weight extraction not yet implemented")
            return None
        except Exception as e:
            print(f"⚠️ Failed to extract attention weights: {e}")
            return None

    def get_model_complexity(self) -> Dict[str, Any]:
        """Get model complexity metrics."""
        return {
            'total_parameters': self._estimate_parameters(),
            'attention_heads': self.n_heads,
            'transformer_layers': self.n_layers,
            'model_dimension': self.d_model,
            'complexity_score': 3,  # High complexity
            'memory_mb': self._estimate_memory_mb(),
            'computational_cost': 'high',
            'performance_tier': 'highest'
        }

    def get_hyperparameter_ranges(self) -> Dict[str, Dict[str, Any]]:
        """Get recommended hyperparameter ranges for optimization."""
        return {
            'n_layers': {'min': 1, 'max': 6, 'default': 2, 'type': 'int'},
            'd_model': {'min': 64, 'max': 512, 'default': 256, 'type': 'int', 'step': 64},
            'n_heads': {'min': 2, 'max': 16, 'default': 4, 'type': 'int'},
            'dropout': {'min': 0.0, 'max': 0.5, 'default': 0.1, 'type': 'float'},
            'learning_rate': {'min': 1e-5, 'max': 1e-2, 'default': 1e-3, 'type': 'float'},
            'batch_size': {'min': 8, 'max': 128, 'default': 32, 'type': 'int'},
            'epochs': {'min': 10, 'max': 200, 'default': 50, 'type': 'int'}
        }

    def optimize_for_dataset(self, data_shape: tuple, missing_rate: float = 0.3) -> Dict[str, Any]:
        """
        Suggest optimal hyperparameters based on dataset characteristics.

        Args:
            data_shape: Shape of the dataset (batch, sequence, features)
            missing_rate: Proportion of missing values

        Returns:
            Dictionary with suggested hyperparameters
        """
        batch_size, seq_len, n_feat = data_shape

        # Adjust model size based on data complexity
        if seq_len < 32:
            suggested_d_model = 128
            suggested_n_layers = 1
        elif seq_len < 64:
            suggested_d_model = 256
            suggested_n_layers = 2
        else:
            suggested_d_model = 512
            suggested_n_layers = 3

        # Adjust based on missing rate
        if missing_rate > 0.5:
            suggested_epochs = 100  # More epochs for high missing rates
        else:
            suggested_epochs = 50

        # Adjust batch size based on available data
        if batch_size < 100:
            suggested_batch_size = min(16, batch_size // 2)
        else:
            suggested_batch_size = 32

        return {
            'd_model': suggested_d_model,
            'n_layers': suggested_n_layers,
            'n_heads': min(8, suggested_d_model // 32),
            'epochs': suggested_epochs,
            'batch_size': suggested_batch_size,
            'learning_rate': 1e-3,
            'dropout': 0.1 if missing_rate < 0.3 else 0.2
        }

    def get_params(self, deep: bool = True) -> Dict[str, Any]:
        """
        Get parameters for this estimator.
        Compatible with Scikit-learn's get_params.
        Args:
            deep (bool): If True, will return the parameters for this estimator and
                         contained subobjects that are estimators.
        Returns:
            dict: Parameter names mapped to their values.
        """
        return {
            "n_features": self.n_features,
            "sequence_len": self.sequence_len,
            "n_layers": self.n_layers,
            "d_model": self.d_model,
            "n_heads": self.n_heads,
            "epochs": self.epochs,
            "batch_size": self.batch_size,
            "learning_rate": self.learning_rate,
            "dropout": self.dropout,
        }

    def set_params(self, **params: Any) -> 'SAITSModel':
        """
        Set the parameters of this estimator.
        Compatible with Scikit-learn's set_params.
        Args:
            **params: Estimator parameters.
        Returns:
            self: Estimator instance.
        """
        for param, value in params.items():
            if hasattr(self, param):
                setattr(self, param, value)
            else:
                warnings.warn(f"Cannot set parameter {param} for SAITSModel.")
        
        # Re-validate and re-initialize if necessary
        self._validate_parameters()
        if self.is_fitted:
            self._initialize_model()
            
        return self
 
    def __repr__(self) -> str:
        """Enhanced string representation of the SAITS model."""
        status = "fitted" if self.is_fitted else "unfitted"
        return (f"SAITSModel("
                f"n_features={self.n_features}, "
                f"sequence_len={self.sequence_len}, "
                f"n_layers={self.n_layers}, "
                f"d_model={self.d_model}, "
                f"n_heads={self.n_heads}, "
                f"status={status})")

# Utility functions for SAITS model
def create_saits_model_from_config(config: Dict[str, Any]) -> SAITSModel:
    """
    Create a SAITS model from configuration dictionary.

    Args:
        config: Configuration dictionary with model parameters

    Returns:
        Configured SAITSModel instance
    """
    return SAITSModel(**config)

def validate_saits_config(config: Dict[str, Any]) -> bool:
    """
    Validate SAITS model configuration.

    Args:
        config: Configuration dictionary to validate

    Returns:
        bool: True if configuration is valid
    """
    required_keys = ['n_features', 'sequence_len']
    for key in required_keys:
        if key not in config:
            print(f"❌ Missing required configuration key: {key}")
            return False

    # Validate d_model and n_heads compatibility
    d_model = config.get('d_model', 256)
    n_heads = config.get('n_heads', 4)

    if d_model % n_heads != 0:
        print(f"❌ d_model ({d_model}) must be divisible by n_heads ({n_heads})")
        return False

    print("✅ SAITS configuration is valid")
    return True
